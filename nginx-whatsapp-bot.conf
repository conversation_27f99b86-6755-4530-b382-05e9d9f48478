# Nginx configuration for WhatsA<PERSON> Bot on sasthra.in
# Place this file in /etc/nginx/sites-available/whatsapp-bot
# Then create symlink: sudo ln -s /etc/nginx/sites-available/whatsapp-bot /etc/nginx/sites-enabled/

server {
    listen 80;
    listen [::]:80;
    server_name sasthra.in www.sasthra.in;

    # Redirect HTTP to HTTPS
    return 301 https://$server_name$request_uri;
}

server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name sasthra.in www.sasthra.in;

    # SSL Configuration (update paths to your SSL certificates)
    ssl_certificate /etc/letsencrypt/live/sasthra.in/fullchain.pem;
    ssl_certificate_key /etc/letsencrypt/live/sasthra.in/privkey.pem;
    
    # SSL Security Settings
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES256-GCM-SHA512:DHE-RSA-AES256-GCM-SHA512:ECDHE-RSA-AES256-GCM-SHA384:DHE-RSA-AES256-GCM-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;

    # Security Headers
    add_header X-Frame-Options DENY;
    add_header X-Content-Type-Options nosniff;
    add_header X-XSS-Protection "1; mode=block";
    add_header Strict-Transport-Security "max-age=31536000; includeSubDomains" always;

    # Logging
    access_log /var/log/nginx/whatsapp-bot-access.log;
    error_log /var/log/nginx/whatsapp-bot-error.log;

    # WhatsApp Bot Webhook Route
    location /webhook {
        proxy_pass http://localhost:5000/webhook;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeout settings for WhatsApp webhooks
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
        
        # Buffer settings
        proxy_buffering off;
        proxy_request_buffering off;
    }

    # Health check endpoint (optional)
    location /health {
        proxy_pass http://localhost:5000/webhook?hub.verify_token=sasthra_whatsapp_verify_token_2025&hub.challenge=health;
        proxy_http_version 1.1;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Your existing website routes (if any)
    location / {
        # Add your existing website configuration here
        # For now, return a simple message
        return 200 "Sasthra.in WhatsApp Bot is running";
        add_header Content-Type text/plain;
    }

    # Deny access to sensitive files
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    # Deny access to backup files
    location ~ ~$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}
