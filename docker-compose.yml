version: '3.8'

services:
  whatsapp-bot:
    build: .
    container_name: sasthra-whatsapp-bot
    ports:
      - "5000:5000"
    environment:
      - PORT=5000
      - FLASK_ENV=production
    env_file:
      - src/.env
    restart: unless-stopped
    volumes:
      - ./logs:/app/logs
    networks:
      - sasthra-network
    healthcheck:
      test: ["CMD", "curl", "-f", "http://localhost:5000/webhook?hub.verify_token=sasthra_whatsapp_verify_token_2025&hub.challenge=health"]
      interval: 30s
      timeout: 10s
      retries: 3
      start_period: 40s

networks:
  sasthra-network:
    driver: bridge

volumes:
  logs:
    driver: local
